/**
 * ASR数据处理工具类
 * 用于处理不同ASR引擎的识别结果，实现智能断句和去重
 */

// 导入在需要时会被使用

export default class AsrProcessor {
  constructor() {
    // Windows Live Caption 相关状态
    this.winLcSentText = '' // 已发送的完整句子文本
    this.winLcMinCharsAfterPeriod = 10 // 句号后至少等待的字符数
    this.lastDisplayCaption = '' // 上次显示的文本
    this.lastTranslationText = '' // 上次翻译的文本
    this.lastRecognizingText = '' // 上次发送的speechRecognizing文本
    this.lastRecognizedText = '' // 上次发送的speechRecognized文本
    this.lastRecognizingTime = 0 // 上次发送speechRecognizing的时间戳
    this.lastRecognizedTime = 0 // 上次发送speechRecognized的时间戳
    this.minInterval = 100 // 最小发送间隔（毫秒）
  }

  /**
   * 处理Windows Live Caption的ASR数据
   * 简化逻辑：按句号分割，句号前发送speechRecognized，剩余发送speechRecognizing
   * 句号后等待一定字符数再发送，避免过早断句
   * @param {Object} data - 包含caption字段的数据
   * @param {Function} handleAsrResult - 处理ASR结果的回调函数
   */
    /**
   * 处理Windows Live Caption的ASR数据
   */
  processWinLcAsrData(data, handleAsrResult) {
    console.log('win lc realtime data', data)
    if(!data.caption) return
    
    let fullText = data.caption.replaceAll('\n', '');
    console.log('lc text', fullText)
    if (!fullText) return

    // 预处理文本，类似C#中的处理
    fullText = fullText.replace(/([A-Z])([A-Z])/g, '$1$2'); // 处理缩写
    fullText = fullText.replace(/([.。?!？！])\s+/g, '$1 '); // 标点后加空格

    // 获取最后一个句子的结束位置
    const sentenceEndRegex = /[.。?!？！]/g;
    let lastEOSIndex = -1;
    
    // 如果最后一个字符是句号，找倒数第二个句号
    if (sentenceEndRegex.test(fullText[fullText.length - 1])) {
      const textWithoutLast = fullText.slice(0, -1);
      const matches = [...textWithoutLast.matchAll(sentenceEndRegex)];
      if (matches.length > 0) {
        lastEOSIndex = matches[matches.length - 1].index;
      }
    } else {
      const matches = [...fullText.matchAll(sentenceEndRegex)];
      if (matches.length > 0) {
        lastEOSIndex = matches[matches.length - 1].index;
      }
    }

    // 提取最后一句话
    let latestCaption = fullText.substring(lastEOSIndex + 1);
    
    // 如果最后一句太短，向前扩展一句
    const SHORT_THRESHOLD = 10;
    if (lastEOSIndex > 0 && new TextEncoder().encode(latestCaption).length < SHORT_THRESHOLD) {
      const prevText = fullText.slice(0, lastEOSIndex);
      const prevMatches = [...prevText.matchAll(sentenceEndRegex)];
      if (prevMatches.length > 0) {
        const prevEOSIndex = prevMatches[prevMatches.length - 1].index;
        latestCaption = fullText.substring(prevEOSIndex + 1);
      }
    }

    // 检查是否有变化
    if (latestCaption === this.lastDisplayCaption) {
      return; // 没有变化，不处理
    }
    this.lastDisplayCaption = latestCaption;

    // 准备翻译用的文本（只包含完整句子）
    const lastEOS = latestCaption.search(/[.。?!？！][^.。?!？！]*$/);
    let translationText = '';
    if (lastEOS !== -1) {
      translationText = latestCaption.substring(0, lastEOS + 1);
    }

    const currentTime = Date.now();

    // 发送正在识别的文本（显示用）- 只有当文本发生变化且满足时间间隔时才发送
    const currentRecognizingText = latestCaption.trim();
    if (currentRecognizingText &&
        currentRecognizingText !== this.lastRecognizingText &&
        (currentTime - this.lastRecognizingTime) >= this.minInterval) {
      this.lastRecognizingText = currentRecognizingText;
      this.lastRecognizingTime = currentTime;
      const recognizingData = {
        source: "win-lc",
        result: currentRecognizingText,
        type: "speechRecognizing"
      };
      handleAsrResult(null, { type: 'speechRecognizing', data: JSON.stringify(recognizingData) });
    }

    // 发送已完成的句子（翻译用）- 只有当完整句子发生变化且满足时间间隔时才发送
    if (translationText &&
        translationText !== this.lastRecognizedText &&
        (currentTime - this.lastRecognizedTime) >= this.minInterval) {
      this.lastRecognizedText = translationText;
      this.lastRecognizedTime = currentTime;
      const completedData = {
        source: "win-lc",
        result: translationText.trim(),
        type: "speechRecognized"
      };
      handleAsrResult(null, { type: 'speechRecognized', data: JSON.stringify(completedData) });
    }

    console.log('display text:', latestCaption);
    console.log('translation text:', translationText);
  }

  /**
   * 按句号分割文本，并判断句号后字符数是否达到要求
   * @param {string} text
   * @param {number} minCharsAfterPeriod
   * @returns {Object}
   */
  splitBySentenceEnd(text, minCharsAfterPeriod = 10) {
    const sentenceEndRegex = /[.。]/g
    let lastValidPeriodIndex = -1
    let match

    while ((match = sentenceEndRegex.exec(text)) !== null) {
      const periodIndex = match.index
      const charsAfter = text.length - (periodIndex + 1)
      if (charsAfter >= minCharsAfterPeriod) {
        lastValidPeriodIndex = periodIndex
      }
    }

    if (lastValidPeriodIndex >= 0) {
      return {
        beforePeriod: text.slice(0, lastValidPeriodIndex + 1).trim(),
        afterPeriod: text.slice(lastValidPeriodIndex + 1).trim(),
        hasNewPeriod: true
      }
    }

    return {
      beforePeriod: '',
      afterPeriod: text.trim(),
      hasNewPeriod: false
    }
  }


  /**
   * 重置Windows Live Caption的状态
   * 在开始新的识别会话时调用
   */
  resetWinLcState() {
    this.winLcSentText = ''
    this.lastDisplayCaption = ''
    this.lastTranslationText = ''
    this.lastRecognizingText = ''
    this.lastRecognizedText = ''
    this.lastRecognizingTime = 0
    this.lastRecognizedTime = 0
  }

  /**
   * 设置最小发送间隔
   * @param {number} interval - 间隔时间（毫秒）
   */
  setMinInterval(interval) {
    this.minInterval = Math.max(50, interval); // 最小50ms
  }

  /**
   * 获取当前Windows Live Caption的状态
   * @returns {Object} 当前状态
   */
  getWinLcState() {
    return {
      sentText: this.winLcSentText,
      pendingText: this.winLcPendingText,
      minCharsAfterPeriod: this.winLcMinCharsAfterPeriod,
      lastRecognizingText: this.lastRecognizingText,
      lastRecognizedText: this.lastRecognizedText,
      minInterval: this.minInterval
    }
  }
}
