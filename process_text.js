/**
 * ASR数据处理工具类
 * 用于处理不同ASR引擎的识别结果，实现智能断句和去重
 */

// 导入在需要时会被使用

export default class AsrProcessor {
  constructor() {
    // Windows Live Caption 相关状态
    this.winLcSentText = '' // 已发送的完整句子文本
    this.winLcMinCharsAfterPeriod = 10 // 句号后至少等待的字符数
  }

  /**
   * 处理Windows Live Caption的ASR数据
   * 简化逻辑：按句号分割，句号前发送speechRecognized，剩余发送speechRecognizing
   * 句号后等待一定字符数再发送，避免过早断句
   * @param {Object} data - 包含caption字段的数据
   * @param {Function} handleAsrResult - 处理ASR结果的回调函数
   */
    /**
   * 处理Windows Live Caption的ASR数据
   */
  processWinLcAsrData(data, handleAsrResult) {
    console.log('win lc realtime data', data)
    if(!data.caption) return
    let fullText = data.caption.replaceAll('\n', '');
    console.log('lc text', fullText)

    if (!fullText) return

    const newText = fullText.slice(this.winLcSentText.length)
    console.log('after slice text：', newText)

    if (!newText.trim()) {
      console.log('没有新文本')
      return
    }

    // 找到最后一个满足“句号后 >=10 字符”的分句点
    const result = this.splitBySentenceEnd(newText, this.winLcMinCharsAfterPeriod)
    console.log('splitBySentenceEnd ', result)
    
    if (result.beforePeriod) {
      this.winLcSentText += result.beforePeriod
      if(result.beforePeriod == '.' || result.beforePeriod == '。'){
        return
      }
      const completedData = {
        source: "win-lc",
        result: result.beforePeriod,
        type: "speechRecognized"
      }
      handleAsrResult(null, { type: 'speechRecognized', data: JSON.stringify(completedData) })
    }

    if (result.afterPeriod) {
      const remainingData = {
        source: "win-lc",
        result: result.afterPeriod,
        type: "speechRecognizing"
      }
      handleAsrResult(null, { type: 'speechRecognizing', data: JSON.stringify(remainingData) })
    }

    console.log('alerdy send text ', this.winLcSentText)
    console.log('ing send text ', result.afterPeriod)
  }

  /**
   * 按句号分割文本，并判断句号后字符数是否达到要求
   * @param {string} text
   * @param {number} minCharsAfterPeriod
   * @returns {Object}
   */
  splitBySentenceEnd(text, minCharsAfterPeriod = 10) {
    const sentenceEndRegex = /[.。]/g
    let lastValidPeriodIndex = -1
    let match

    while ((match = sentenceEndRegex.exec(text)) !== null) {
      const periodIndex = match.index
      const charsAfter = text.length - (periodIndex + 1)
      if (charsAfter >= minCharsAfterPeriod) {
        lastValidPeriodIndex = periodIndex
      }
    }

    if (lastValidPeriodIndex >= 0) {
      return {
        beforePeriod: text.slice(0, lastValidPeriodIndex + 1).trim(),
        afterPeriod: text.slice(lastValidPeriodIndex + 1).trim(),
        hasNewPeriod: true
      }
    }

    return {
      beforePeriod: '',
      afterPeriod: text.trim(),
      hasNewPeriod: false
    }
  }


  /**
   * 重置Windows Live Caption的状态
   * 在开始新的识别会话时调用
   */
  resetWinLcState() {
    this.winLcSentText = ''
  }

  /**
   * 获取当前Windows Live Caption的状态
   * @returns {Object} 当前状态
   */
  getWinLcState() {
    return {
      sentText: this.winLcSentText,
      pendingText: this.winLcPendingText,
      minCharsAfterPeriod: this.winLcMinCharsAfterPeriod
    }
  }
}
