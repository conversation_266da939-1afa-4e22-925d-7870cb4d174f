/**
 * ASR数据处理工具类
 * 用于处理不同ASR引擎的识别结果，实现智能断句和去重
 */

// 导入在需要时会被使用

export default class AsrProcessor {
  constructor() {
    // Windows Live Caption 相关状态
    this.winLcSentText = '' // 已发送的完整句子文本
    this.winLcMinCharsAfterPeriod = 10 // 句号后至少等待的字符数

    // 对应C#中的状态
    this.displayOriginalCaption = '' // 对应C# Caption.DisplayOriginalCaption
    this.originalCaption = '' // 对应C# Caption.OriginalCaption

    // 常量定义，对应C# TextUtil
    this.PUNC_EOS = ['.', '?', '!', '。', '？', '！']
    this.SHORT_THRESHOLD = 10
    this.VERYLONG_THRESHOLD = 220
  }

  /**
   * 处理Windows Live Caption的ASR数据
   * 简化逻辑：按句号分割，句号前发送speechRecognized，剩余发送speechRecognizing
   * 句号后等待一定字符数再发送，避免过早断句
   * @param {Object} data - 包含caption字段的数据
   * @param {Function} handleAsrResult - 处理ASR结果的回调函数
   */
    /**
   * 处理Windows Live Caption的ASR数据
   */
  processWinLcAsrData(data, handleAsrResult) {
    console.log('win lc realtime data', data)

    if (!data.caption) return

    let fullText = data.caption
    console.log('原始文本:', fullText)

    if (!fullText) return

    // 预处理文本，对应C#中的预处理逻辑
    // 处理缩写：移除两个大写字母之间的点
    fullText = fullText.replace(/([A-Z])\s*\.\s*([A-Z])(?![A-Za-z]+)/g, '$1$2')
    // 如果缩写后跟单词，保留空格
    fullText = fullText.replace(/([A-Z])\s*\.\s*([A-Z])(?=[A-Za-z]+)/g, '$1 $2')
    // 标点符号周围的空格处理
    fullText = fullText.replace(/\s*([.!?,])\s*/g, '$1 ')
    // 中日文标点不需要空格
    fullText = fullText.replace(/\s*([。！？，、])\s*/g, '$1')
    // 处理换行符
    fullText = this.replaceNewlines(fullText, 40) // MEDIUM_THRESHOLD = 40

    console.log('预处理后文本:', fullText)

    // 获取最后一个句子的结束位置
    let lastEOSIndex = -1
    if (this.isPuncEOS(fullText[fullText.length - 1])) {
      // 如果最后一个字符是句号，找倒数第二个句号
      const textWithoutLast = fullText.slice(0, -1)
      lastEOSIndex = this.lastIndexOfAny(textWithoutLast, this.PUNC_EOS)
    } else {
      lastEOSIndex = this.lastIndexOfAny(fullText, this.PUNC_EOS)
    }

    let latestCaption = fullText.substring(lastEOSIndex + 1)
    console.log('提取的最新句子:', latestCaption)

    // 如果最后一句太短，向前扩展一句
    if (lastEOSIndex > 0 && this.getByteLength(latestCaption) < this.SHORT_THRESHOLD) {
      const prevText = fullText.slice(0, lastEOSIndex)
      const prevEOSIndex = this.lastIndexOfAny(prevText, this.PUNC_EOS)
      latestCaption = fullText.substring(prevEOSIndex + 1)
      console.log('扩展后的句子:', latestCaption)
    }

    // 检查是否需要更新显示内容
    if (this.displayOriginalCaption !== latestCaption) {
      this.displayOriginalCaption = latestCaption

      // 如果句子太长，截断显示
      let displayText = this.shortenDisplaySentence(this.displayOriginalCaption, this.VERYLONG_THRESHOLD)

      console.log('最终显示文本:', displayText)
      console.log('---')
    }
  }

  /**
   * 按句号分割文本，并判断句号后字符数是否达到要求
   * @param {string} text
   * @param {number} minCharsAfterPeriod
   * @returns {Object}
   */
  splitBySentenceEnd(text, minCharsAfterPeriod = 10) {
    const sentenceEndRegex = /[.。]/g
    let lastValidPeriodIndex = -1
    let match

    while ((match = sentenceEndRegex.exec(text)) !== null) {
      const periodIndex = match.index
      const charsAfter = text.length - (periodIndex + 1)
      if (charsAfter >= minCharsAfterPeriod) {
        lastValidPeriodIndex = periodIndex
      }
    }

    if (lastValidPeriodIndex >= 0) {
      return {
        beforePeriod: text.slice(0, lastValidPeriodIndex + 1).trim(),
        afterPeriod: text.slice(lastValidPeriodIndex + 1).trim(),
        hasNewPeriod: true
      }
    }

    return {
      beforePeriod: '',
      afterPeriod: text.trim(),
      hasNewPeriod: false
    }
  }


  /**
   * 判断字符是否为句子结束标点
   */
  isPuncEOS(char) {
    return this.PUNC_EOS.includes(char)
  }

  /**
   * 查找字符串中任意字符的最后出现位置
   */
  lastIndexOfAny(str, chars) {
    let lastIndex = -1
    for (let char of chars) {
      const index = str.lastIndexOf(char)
      if (index > lastIndex) {
        lastIndex = index
      }
    }
    return lastIndex
  }

  /**
   * 获取字符串的UTF-8字节长度
   */
  getByteLength(str) {
    return new TextEncoder().encode(str).length
  }

  /**
   * 判断字符是否为中日韩字符
   */
  isCJChar(char) {
    const code = char.charCodeAt(0)
    return (code >= 0x4E00 && code <= 0x9FFF) ||  // CJK统一汉字
           (code >= 0x3400 && code <= 0x4DBF) ||  // CJK扩展A
           (code >= 0x3040 && code <= 0x30FF)     // 平假名和片假名
  }

  /**
   * 替换换行符，对应C# TextUtil.ReplaceNewlines
   */
  replaceNewlines(text, byteThreshold) {
    const splits = text.split('\n')
    for (let i = 0; i < splits.length; i++) {
      splits[i] = splits[i].trim()
      if (i === splits.length - 1) continue

      const lastChar = splits[i][splits[i].length - 1]
      if (this.getByteLength(splits[i]) >= byteThreshold) {
        splits[i] += this.isCJChar(lastChar) ? '。' : '. '
      } else {
        splits[i] += this.isCJChar(lastChar) ? '——' : '—'
      }
    }
    return splits.join('')
  }

  /**
   * 缩短显示句子，对应C# TextUtil.ShortenDisplaySentence
   */
  shortenDisplaySentence(text, maxByteLength) {
    const PUNC_COMMA = [',', '，', '、', '—', '\n']
    const allPunc = [...this.PUNC_EOS, ...PUNC_COMMA]

    while (this.getByteLength(text) >= maxByteLength) {
      let puncIndex = -1
      for (let char of allPunc) {
        const index = text.indexOf(char)
        if (index >= 0 && (puncIndex === -1 || index < puncIndex)) {
          puncIndex = index
        }
      }
      if (puncIndex < 0 || puncIndex + 1 >= text.length) break
      text = text.substring(puncIndex + 1)
    }
    return text
  }

  /**
   * 重置Windows Live Caption的状态
   * 在开始新的识别会话时调用
   */
  resetWinLcState() {
    this.displayOriginalCaption = ''
  }

  /**
   * 获取当前Windows Live Caption的状态
   * @returns {Object} 当前状态
   */
  getWinLcState() {
    return {
      sentText: this.winLcSentText,
      pendingText: this.winLcPendingText,
      minCharsAfterPeriod: this.winLcMinCharsAfterPeriod
    }
  }
}
