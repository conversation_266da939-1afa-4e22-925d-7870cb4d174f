﻿<Page
    x:Class="LiveCaptionsTranslator.SettingPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:LiveCaptionsTranslator"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="SettingPage"
    JournalEntry.KeepAlive="False"
    mc:Ignorable="d">

    <Grid Margin="15">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <StackPanel Grid.Column="0" Orientation="Vertical">
            <StackPanel Margin="15,0,0,0" Orientation="Vertical">
                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                    <TextBlock Margin="2.5,0,0,5" Text="LiveCaptions" />
                    <ui:Flyout x:Name="LiveCaptionsInfoFlyout">
                        <TextBlock Width="300" TextWrapping="Wrap">
                            <Run Text="After Windows 11 version 24H2, you can only change the" />
                            <Run FontWeight="Bold" Text="source language" />
                            <Run Text="in LiveCaptions." />
                            <Run FontWeight="Bold" Text="&#x0A;&#x0A;Note:" />
                            <Run Text="Please click" />
                            <Run FontWeight="Bold" Text="&quot;Hide&quot;" />
                            <Run Text="to hide LiveCaptions instead of closing it directly." />
                        </TextBlock>
                    </ui:Flyout>
                    <Button
                        x:Name="LiveCaptionsInfo"
                        Width="15"
                        Height="15"
                        Margin="-5,-3,0,0"
                        Padding="0"
                        Background="Transparent"
                        BorderThickness="0"
                        MouseEnter="LiveCaptionsInfo_MouseEnter"
                        MouseLeave="LiveCaptionsInfo_MouseLeave">
                        <ui:SymbolIcon Symbol="Info16" />
                    </Button>
                </StackPanel>
                <ui:Button
                    x:Name="LiveCaptionsButton"
                    Width="120"
                    Height="30"
                    Padding="11,4,11,4"
                    Click="LiveCaptionsButton_click">
                    <TextBlock x:Name="ButtonText" Text="Show" />
                </ui:Button>
            </StackPanel>

            <StackPanel Margin="15,10,0,0" Orientation="Vertical">
                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                    <TextBlock Margin="2.5,0,0,5" Text="API Interval" />
                    <ui:Flyout x:Name="FrequencyInfoFlyout">
                        <TextBlock Width="300" TextWrapping="Wrap">
                            <Run Text="Determines the frequency of translate API calls. The smaller it is, the more frequent API calls." />
                            <Run Text="&#x0A;The translate API is called once after the caption changes" />
                            <Run FontWeight="SemiBold" Text="[API Interval]" />
                            <Run Text="times." />
                        </TextBlock>
                    </ui:Flyout>
                    <Button
                        x:Name="FrequencyInfo"
                        Width="15"
                        Height="15"
                        Margin="-5,-3,0,0"
                        Padding="0"
                        Background="Transparent"
                        BorderThickness="0"
                        MouseEnter="FrequencyInfo_MouseEnter"
                        MouseLeave="FrequencyInfo_MouseLeave">
                        <ui:SymbolIcon Symbol="Info16" />
                    </Button>
                </StackPanel>
                <StackPanel Orientation="Horizontal">
                    <Slider
                        Width="120"
                        Margin="0,0,5,0"
                        AutoToolTipPlacement="TopLeft"
                        AutoToolTipPrecision="0"
                        Maximum="10"
                        Value="{Binding MaxSyncInterval, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                </StackPanel>
            </StackPanel>
        </StackPanel>

        <StackPanel Grid.Column="1" Orientation="Vertical">
            <StackPanel Margin="15,0,0,0" Orientation="Vertical">
                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                    <TextBlock Margin="2.5,0,0,5" Text="Translate API" />
                    <ui:Flyout x:Name="TranslateAPIInfoFlyout">
                        <TextBlock
                            Width="300"
                            Text="Except for Google and Google2, all other APIs require configuring before they can be used."
                            TextWrapping="Wrap" />
                    </ui:Flyout>
                    <Button
                        x:Name="TranslateAPIInfo"
                        Width="15"
                        Height="15"
                        Margin="-5,-3,0,0"
                        Padding="0"
                        Background="Transparent"
                        BorderThickness="0"
                        MouseEnter="TranslateAPIInfo_MouseEnter"
                        MouseLeave="TranslateAPIInfo_MouseLeave">
                        <ui:SymbolIcon Symbol="Info16" />
                    </Button>
                </StackPanel>
                <ComboBox
                    x:Name="TranslateAPIBox"
                    Width="150"
                    Height="30"
                    Padding="10,4,10,7"
                    FontSize="13.3"
                    SelectedItem="{Binding ApiName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    SelectionChanged="TranslateAPIBox_SelectionChanged" />
            </StackPanel>

            <StackPanel Margin="15,10,0,0" Orientation="Vertical">
                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                    <TextBlock
                        Margin="2.5,0,0,5"
                        VerticalAlignment="Center"
                        Text="Target Language" />
                    <ui:Flyout x:Name="TargetLangInfoFlyout">
                        <TextBlock Width="350" TextWrapping="Wrap">
                            <Run FontWeight="DemiBold" TextDecorations="Underline" Text="&quot;There isn’t the target language I expected!&quot;" />
                            <Run Text="&#x0A;You can directly edit the content of this combobox to customize the language, and it is recommended to follow the" />
                            <Run FontWeight="Bold" Text="BCP 47 language tag." />
                            <Run FontWeight="Bold" Text="&#x0A;&#x0A;Note:" />
                            <Run Text="Some of APIs (such as DeepL) needs another way to define target language, see their official docs for more details." />
                            <Run Text="&#x0A;No need to consider this for included target languages, since we've built in tag mappings. But if your expected language isn't in the list, keep this in mind." />
                        </TextBlock>
                    </ui:Flyout>
                    <Button
                        x:Name="TargetLangInfo"
                        Width="15"
                        Height="15"
                        Margin="-5,-3,0,0"
                        Padding="0"
                        Background="Transparent"
                        BorderThickness="0"
                        MouseEnter="TargetLangInfo_MouseEnter"
                        MouseLeave="TargetLangInfo_MouseLeave">
                        <ui:SymbolIcon Symbol="Info16" />
                    </Button>
                </StackPanel>
                <ComboBox
                    x:Name="TargetLangBox"
                    Width="150"
                    Height="30"
                    Padding="10,4,10,7"
                    FontSize="13.3"
                    IsEditable="True"
                    LostFocus="TargetLangBox_LostFocus"
                    SelectionChanged="TargetLangBox_SelectionChanged" />
            </StackPanel>
        </StackPanel>

        <StackPanel Grid.Column="2" Orientation="Vertical">
            <StackPanel Margin="15,0,0,0" Orientation="Vertical">
                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                    <TextBlock Margin="2.5,0,0,5" Text="API Setting" />
                </StackPanel>
                <ui:Button
                    x:Name="APISettingButton"
                    Width="100"
                    Height="30"
                    Padding="11,4,11,4"
                    Click="APISettingButton_click">
                    <TextBlock Text="Open" />
                </ui:Button>
            </StackPanel>
            <StackPanel Margin="15,10,0,0" Orientation="Vertical">
                <TextBlock Margin="2.5,0,0,5" Text="Show Latency" />
                <ui:ToggleSwitch
                    Width="100"
                    Height="30"
                    Padding="10,4,10,7"
                    IsChecked="{Binding MainWindow.LatencyShow, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    OffContent="Off"
                    OnContent="On" />
            </StackPanel>
        </StackPanel>

        <StackPanel Grid.Column="3" Orientation="Vertical">
            <StackPanel Margin="15,0,0,0" Orientation="Vertical">
                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                    <TextBlock Margin="2.5,0,0,5" Text="Contexts" />
                    <ui:Flyout x:Name="CaptionLogMaxInfoFlyout">
                        <TextBlock Width="300" TextWrapping="Wrap">
                            <Run FontWeight="Bold" Text="Contexts:" />
                            <Run Text="Determines the number of displayed cards when" />
                            <Run FontWeight="Bold" Text="Log Cards" />
                            <Run Text="is enabled, as well as the number of context sentences when" />
                            <Run FontWeight="Bold" Text="Context Aware" />
                            <Run Text="is enabled." />
                            <Run FontWeight="Bold" Text="&#x0A;Overlay Sentences:" />
                            <Run Text="The max number of sentences displayed in the translation box within the Overlay Window." />
                            <Run FontWeight="Bold" Text="&#x0A;&#x0A;Note:" />
                            <Run Text="The number of Contexts must be" />
                            <Run FontWeight="Bold" Text="greater than or equal" />
                            <Run Text="to the number of Overlay Sentences. If not met, the program will automatically adjust them." />
                        </TextBlock>
                    </ui:Flyout>
                    <Button
                        x:Name="CaptionLogMaxInfo"
                        Width="15"
                        Height="15"
                        Margin="-5,-3,0,0"
                        Padding="0"
                        Background="Transparent"
                        BorderThickness="0"
                        MouseEnter="CaptionLogMaxInfo_MouseEnter"
                        MouseLeave="CaptionLogMaxInfo_MouseLeave">
                        <ui:SymbolIcon Symbol="Info16" />
                    </Button>
                </StackPanel>
                <ComboBox
                    x:Name="CaptionLogMax"
                    Width="135"
                    Height="30"
                    Padding="10,4,10,7"
                    FontSize="13.3"
                    SelectedValue="{Binding MainWindow.CaptionLogMax, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    SelectedValuePath="Content"
                    SelectionChanged="CaptionLogMax_SelectionChanged">
                    <ComboBoxItem Content="1" />
                    <ComboBoxItem Content="2" />
                    <ComboBoxItem Content="3" />
                    <ComboBoxItem Content="4" />
                    <ComboBoxItem Content="5" />
                    <ComboBoxItem Content="6" />
                </ComboBox>
            </StackPanel>

            <StackPanel Margin="15,10,0,0" Orientation="Vertical">
                <TextBlock Margin="2.5,0,0,5" Text="Overlay Sentences" />
                <ComboBox
                    x:Name="OverlayHistoryMax"
                    Width="135"
                    Height="30"
                    Padding="10,4,10,7"
                    FontSize="13.3"
                    SelectedValue="{Binding OverlayWindow.HistoryMax, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    SelectedValuePath="Content"
                    SelectionChanged="OverlayHistoryMax_SelectionChanged">
                    <ComboBoxItem Content="0" />
                    <ComboBoxItem Content="1" />
                    <ComboBoxItem Content="2" />
                    <ComboBoxItem Content="3" />
                    <ComboBoxItem Content="4" />
                    <ComboBoxItem Content="5" />
                    <ComboBoxItem Content="6" />
                </ComboBox>
            </StackPanel>
        </StackPanel>

        <StackPanel Grid.Column="4" Orientation="Vertical">
            <StackPanel Margin="15,0,0,0" Orientation="Vertical">
                <TextBlock Margin="2.5,0,0,5" Text="Context Aware" />
                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                    <ui:ToggleSwitch
                        Width="90"
                        Height="30"
                        Padding="10,4,10,7"
                        IsChecked="{Binding ContextAware, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        OffContent="Off"
                        OnContent="On" />
                    <Button
                        x:Name="ContextAwareInfo"
                        Width="15"
                        Height="15"
                        Margin="-5,-3,0,0"
                        Padding="0"
                        Background="Transparent"
                        BorderThickness="0"
                        MouseEnter="ContextAwareInfo_MouseEnter"
                        MouseLeave="ContextAwareInfo_MouseLeave">
                        <ui:SymbolIcon Symbol="Info16" />
                    </Button>
                    <ui:Flyout x:Name="ContextAwareInfoFlyout">
                        <TextBlock Width="300" TextWrapping="Wrap">
                            <Run Text="Translate in context." />
                            <Run Text="&#x0A;It can improve translation accuracy, but will consume more tokens." />
                        </TextBlock>
                    </ui:Flyout>
                </StackPanel>
            </StackPanel>
        </StackPanel>
    </Grid>
</Page>
