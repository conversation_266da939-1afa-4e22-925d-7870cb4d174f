﻿<Page
    x:Class="LiveCaptionsTranslator.CaptionPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:LiveCaptionsTranslator"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="CaptionPage"
    JournalEntry.KeepAlive="True"
    mc:Ignorable="d">

    <Grid Margin="5,5,15,5">
        <Grid.RowDefinitions>
            <RowDefinition x:Name="OriginalCaption_Row" Height="Auto" />
            <RowDefinition x:Name="TranslatedCaption_Row" Height="Auto" />
            <RowDefinition x:Name="CaptionLogCard_Row" Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.Resources>
            <Style x:Key="CaptionBlockStyle" TargetType="TextBlock">
                <Setter Property="TextWrapping" Value="Wrap" />
                <Setter Property="VerticalAlignment" Value="Stretch" />
            </Style>
        </Grid.Resources>

        <ui:Card
            Grid.Row="0"
            Padding="8"
            VerticalAlignment="Stretch">
            <TextBlock
                x:Name="OriginalCaption"
                Background="Transparent"
                Cursor="Hand"
                FontSize="15"
                MouseLeftButtonDown="TextBlock_MouseLeftButtonDown"
                Style="{StaticResource CaptionBlockStyle}"
                Text="{Binding DisplayOriginalCaption, UpdateSourceTrigger=PropertyChanged}"
                ToolTip="Click To Copy" />
        </ui:Card>

        <ui:Card
            Grid.Row="1"
            MinHeight="43"
            Margin="0,3,0,0"
            Padding="8"
            VerticalAlignment="Stretch">
            <TextBlock
                x:Name="TranslatedCaption"
                Background="Transparent"
                Cursor="Hand"
                FontSize="18"
                MouseLeftButtonDown="TextBlock_MouseLeftButtonDown"
                Style="{StaticResource CaptionBlockStyle}"
                Text="{Binding DisplayTranslatedCaption, UpdateSourceTrigger=PropertyChanged}"
                ToolTip="Click To Copy" />
        </ui:Card>

        <ui:Card
            x:Name="CaptionLogCard"
            Grid.Row="2"
            Margin="0,0,0,5"
            Padding="8"
            VerticalAlignment="Top"
            Background="Transparent"
            BorderBrush="#00000000">
            <ItemsControl
                x:Name="CaptionLogItems"
                MinHeight="50"
                ItemsSource="{Binding DisplayContexts, UpdateSourceTrigger=PropertyChanged}"
                VirtualizingPanel.IsVirtualizing="True">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border
                            Margin="0,0,0,5"
                            Padding="10,5,10,5"
                            BorderBrush="#20000000"
                            BorderThickness="0,1,0,0">
                            <Grid Grid.Row="0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>

                                <ui:Card
                                    Grid.Row="0"
                                    Padding="8"
                                    VerticalAlignment="Stretch">
                                    <TextBlock
                                        x:Name="OriginalCaptionLog"
                                        Background="Transparent"
                                        Cursor="Hand"
                                        FontSize="12"
                                        MouseLeftButtonDown="TextBlock_MouseLeftButtonDown"
                                        Style="{StaticResource CaptionBlockStyle}"
                                        Text="{Binding SourceText}"
                                        ToolTip="Click To Copy" />
                                </ui:Card>
                                <ui:Card
                                    Grid.Row="1"
                                    Margin="0,3,0,0"
                                    Padding="8"
                                    VerticalAlignment="Stretch">
                                    <TextBlock
                                        x:Name="TranslatedCaptionLog"
                                        Background="Transparent"
                                        Cursor="Hand"
                                        FontSize="16"
                                        MouseLeftButtonDown="TextBlock_MouseLeftButtonDown"
                                        Style="{StaticResource CaptionBlockStyle}"
                                        Text="{Binding TranslatedText}"
                                        ToolTip="Click To Copy" />
                                </ui:Card>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ui:Card>

    </Grid>
</Page>
