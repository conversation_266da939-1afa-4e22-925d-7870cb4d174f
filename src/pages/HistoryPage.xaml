﻿<Page
    x:Class="LiveCaptionsTranslator.HistoryPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:LiveCaptionsTranslator"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="HistoryPage"
    HorizontalAlignment="Left"
    VerticalAlignment="Top"
    JournalEntry.KeepAlive="False"
    mc:Ignorable="d">
    <Grid Margin="15" VerticalAlignment="Top">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>

            <StackPanel
                MaxHeight="34.94"
                Margin="0,0,3,0"
                HorizontalAlignment="Left"
                Orientation="Horizontal">
                <ui:Button
                    x:Name="PageDown"
                    Appearance="Transparent"
                    Click="PageDown_click"
                    Icon="{ui:SymbolIcon chevronLeft12,
                                         Filled=False}"
                    MouseOverBackground="#07000000"
                    ToolTip="Previous " />

                <ui:TextBlock
                    x:Name="PageNumber"
                    Margin="10,0,10,0"
                    VerticalAlignment="Center"
                    FontSize="15"
                    Text="1/1" />

                <ui:Button
                    x:Name="PageUp"
                    Appearance="Transparent"
                    Click="PageUp_click"
                    Icon="{ui:SymbolIcon chevronRight12,
                                         Filled=False}"
                    MouseOverBackground="#07000000"
                    ToolTip="Next Page" />

                <ComboBox
                    x:Name="HistoryMaxRow"
                    MinHeight="0"
                    Margin="5,0,0,0"
                    Padding="10,4,10,7"
                    VerticalAlignment="Stretch"
                    SelectedIndex="1">
                    <ComboBoxItem Content="20/page" Tag="20" />
                    <ComboBoxItem Content="30/page" Tag="30" />
                    <ComboBoxItem Content="50/page" Tag="50" />
                    <ComboBoxItem Content="100/page" Tag="100" />
                </ComboBox>

                <ui:AutoSuggestBox
                    x:Name="HistorySearchBox"
                    MinWidth="200"
                    MaxWidth="200"
                    Margin="5,0,0,0"
                    PlaceholderText="Search"
                    QuerySubmitted="HistorySearchBox_QuerySubmitted"
                    TextChanged="HistorySearchBox_TextChanged" />

            </StackPanel>

            <StackPanel
                Grid.Column="1"
                MaxHeight="34.94"
                Margin="3,0,3,0"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <ui:Button
                    x:Name="Export"
                    Margin="0,0,0,0"
                    HorizontalAlignment="Right"
                    Appearance="Transparent"
                    Click="Export_click"
                    Icon="{ui:SymbolIcon ArrowDown12,
                                         Filled=False}"
                    MouseOverBackground="#07000000"
                    ToolTip="Export" />
                <ui:Button
                    x:Name="Delete"
                    Margin="5,0,0,0"
                    HorizontalAlignment="Right"
                    Appearance="Transparent"
                    Click="Delete_click"
                    Icon="{ui:SymbolIcon Delete12,
                                         Filled=False}"
                    MouseOverBackground="#07000000"
                    ToolTip="Delete All" />

                <ui:Button
                    x:Name="Refresh"
                    Margin="5,0,0,0"
                    HorizontalAlignment="Right"
                    Appearance="Transparent"
                    Click="Refresh_click"
                    Icon="{ui:SymbolIcon ArrowClockwise12,
                                         Filled=False}"
                    MouseOverBackground="#07000000"
                    ToolTip="Refresh" />
            </StackPanel>
        </Grid>

        <DataGrid
            x:Name="HistoryDataGrid"
            Grid.Row="1"
            Margin="0,5,20,0"
            VerticalAlignment="Stretch"
            AutoGenerateColumns="False"
            BorderThickness="0,0,0,0"
            EnableRowVirtualization="True"
            GridLinesVisibility="Horizontal"
            IsReadOnly="True"
            VirtualizingPanel.IsVirtualizing="True"
            VirtualizingPanel.VirtualizationMode="Recycling">
            <DataGrid.VerticalGridLinesBrush>
                <LinearGradientBrush MappingMode="Absolute" EndPoint="0,3">
                    <GradientStop Offset="0.33" Color="#18FFFFFF" />
                    <GradientStop Offset="1" Color="Transparent" />
                </LinearGradientBrush>
            </DataGrid.VerticalGridLinesBrush>
            <DataGrid.HorizontalGridLinesBrush>
                <LinearGradientBrush MappingMode="Absolute" EndPoint="0,3">
                    <GradientStop Offset="0.33" Color="#18FFFFFF" />
                    <GradientStop Offset="1" Color="#33000000" />
                </LinearGradientBrush>
            </DataGrid.HorizontalGridLinesBrush>
            <DataGrid.BorderBrush>
                <LinearGradientBrush MappingMode="Absolute" EndPoint="0,3">
                    <GradientStop Offset="0.33" Color="#18FFFFFF" />
                    <GradientStop Offset="1" Color="Transparent" />
                </LinearGradientBrush>
            </DataGrid.BorderBrush>
            <DataGrid.Columns>
                <DataGridTemplateColumn Width="0.4*" Header="Time">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock
                                Text="{Binding Timestamp}"
                                TextWrapping="Wrap"
                                ToolTip="{Binding TimestampFull}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <DataGridTemplateColumn Width="*" Header="Caption">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding SourceText}" TextWrapping="Wrap" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <DataGridTemplateColumn Width="*" Header="Translated">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding TranslatedText}" TextWrapping="Wrap" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <DataGridTemplateColumn Width="0.3*" Header="API">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding ApiUsed}" TextWrapping="Wrap" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        <ui:SnackbarPresenter x:Name="SnackbarHost" Grid.Row="0" />
    </Grid>
</Page>
