<ui:FluentWindow
    x:Class="LiveCaptionsTranslator.WelcomeWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:LiveCaptionsTranslator"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="Getting Started"
    Width="700"
    Height="490"
    ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
    ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
    ExtendsContentIntoTitleBar="True"
    ResizeMode="NoResize"
    WindowBackdropType="Mica"
    WindowStartupLocation="CenterScreen"
    WindowStyle="None"
    mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <ui:TitleBar
            Grid.Row="0"
            Height="27"
            ShowMaximize="False"
            ShowMinimize="False">
            <ui:TitleBar.Header>
                <ui:TextBlock Padding="15,0,0,0" VerticalAlignment="Center">
                    Getting Started
                </ui:TextBlock>
            </ui:TitleBar.Header>
        </ui:TitleBar>

        <StackPanel Grid.Row="1" Background="{ui:ThemeResource SystemFillColorNeutralBackgroundBrush}">
            <TextBlock
                Margin="20"
                HorizontalAlignment="Left"
                FontSize="24"
                FontWeight="Bold"
                Text="Welcome to LiveCaptions Translator!"
                TextAlignment="Left" />
            <ui:TextBlock
                Margin="20,10,20,10"
                TextAlignment="Left"
                TextWrapping="Wrap">
                <Run Text="LiveCaptions Translator is based on Windows LiveCaptions." />
                <Run Text="Before your first running, you need to configure it according to the following steps:&#x0A;" />
                <Run Text="&#x0A;1. Open Windows LiveCaptions and follow the guide to download the necessary files for on-device speech recognition and language packs." />
                <Run Text="&#x0A;2. Click the" />
                <Run FontWeight="Bold" Text="⚙️ gear" />
                <Run Text="icon in Windows LiveCaptions to open the settings menu, and select" />
                <Run FontWeight="Bold" Text="&quot;Position &gt; Overlaid on screen&quot;" />
                <Run Text=".&#x0A;3. Click the" />
                <Run FontWeight="Bold" Text="&quot;Hide&quot;" />
                <Run Text="button located in the setting page of LiveCaptions Translator to hide Windows LiveCaptions.&#x0A;" />
                <Run Text="&#x0A;The program has automatically navigated to the setting page and opened Windows LiveCaptions." />
                <Run Text="After completing the configuration, you can switch back to the main page and enjoy real-time audio translation.&#x0A;" />
                <Run Text="&#x0A;For more details, visit our wiki: " />
                <Hyperlink NavigateUri="https://github.com/SakiRinn/LiveCaptions-Translator/wiki" RequestNavigate="Hyperlink_RequestNavigate">
                    https://github.com/SakiRinn/LiveCaptions-Translator/wiki
                </Hyperlink>
            </ui:TextBlock>

            <ui:Button
                Margin="0,10,0,0"
                Padding="30,10,30,10"
                HorizontalAlignment="Center"
                Click="CloseButton_Click"
                Content="I have fully understood and configured." />
        </StackPanel>
    </Grid>
</ui:FluentWindow>