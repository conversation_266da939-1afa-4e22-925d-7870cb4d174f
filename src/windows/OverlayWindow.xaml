﻿<Window
    x:Class="LiveCaptionsTranslator.OverlayWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:LiveCaptionsTranslator"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="Overlay Window"
    Width="650"
    Height="135"
    MinWidth="650"
    MinHeight="135"
    AllowsTransparency="True"
    Background="Transparent"
    MouseEnter="Window_MouseEnter"
    MouseLeave="Window_MouseLeave"
    ResizeMode="CanResize"
    ShowInTaskbar="False"
    Topmost="True"
    WindowStyle="None"
    mc:Ignorable="d">

    <Window.Resources>
        <Style x:Key="TransparentThumbStyle" TargetType="Thumb">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderBrush" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Opacity" Value="0" />
        </Style>
    </Window.Resources>

    <Grid>
        <Border
            x:Name="BorderBackground"
            Margin="5"
            Background="#80000000"
            CornerRadius="8"
            MouseLeftButtonDown="Border_MouseLeftButtonDown">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Grid.Resources>
                    <Style x:Key="CaptionBlockStyle" TargetType="TextBlock">
                        <Setter Property="TextWrapping" Value="Wrap" />
                        <Setter Property="VerticalAlignment" Value="Stretch" />
                    </Style>
                </Grid.Resources>

                <ui:Card
                    x:Name="OriginalCaptionCard"
                    Grid.Row="0"
                    Margin="5,3,5,1"
                    Padding="8"
                    VerticalAlignment="Top"
                    Background="Transparent"
                    BorderThickness="0">
                    <TextBlock
                        x:Name="OriginalCaption"
                        FontSize="15"
                        Foreground="#FFFFFFFF"
                        Style="{StaticResource CaptionBlockStyle}"
                        Text="{Binding OverlayOriginalCaption, UpdateSourceTrigger=PropertyChanged}">
                        <TextBlock.Effect>
                            <DropShadowEffect
                                x:Name="OriginalCaptionShadow"
                                BlurRadius="5"
                                Direction="0"
                                ShadowDepth="0"
                                Color="Black" />
                        </TextBlock.Effect>
                    </TextBlock>
                </ui:Card>
                <ui:Card
                    x:Name="TranslatedCaptionCard"
                    Grid.Row="1"
                    Margin="5,1,5,3"
                    Padding="8"
                    VerticalAlignment="Top"
                    Background="Transparent"
                    BorderThickness="0">
                    <TextBlock
                        x:Name="TranslatedCaption"
                        FontSize="18"
                        Foreground="#FFFFFFFF"
                        Style="{StaticResource CaptionBlockStyle}"
                        Text="{Binding OverlayTranslatedCaption, UpdateSourceTrigger=PropertyChanged}">
                        <TextBlock.Effect>
                            <DropShadowEffect
                                x:Name="TranslatedCaptionShadow"
                                BlurRadius="5"
                                Direction="0"
                                ShadowDepth="0"
                                Color="Black" />
                        </TextBlock.Effect>
                    </TextBlock>
                </ui:Card>

                <StackPanel
                    x:Name="ControlPanel"
                    Grid.Row="1"
                    Margin="0,0,5,5"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Bottom"
                    Orientation="Horizontal">
                    <ui:Button
                        x:Name="FontIncrease"
                        Background="#FFAAAAAA"
                        Click="FontIncrease_Click"
                        Cursor="Hand"
                        Icon="{ui:SymbolIcon fontIncrease20,
                                             Filled=False}"
                        ToolTip="Font Increase" />
                    <ui:Button
                        x:Name="FontDecrease"
                        Background="#FFAAAAAA"
                        Click="FontDecrease_Click"
                        Cursor="Hand"
                        Icon="{ui:SymbolIcon fontDecrease20,
                                             Filled=False}"
                        ToolTip="Font Decrease" />
                    <ui:Button
                        x:Name="FontColorCycle"
                        Background="#FFAAAAAA"
                        Click="FontColorCycle_Click"
                        Cursor="Hand"
                        Icon="{ui:SymbolIcon color16,
                                             Filled=False}"
                        ToolTip="Font Color" />
                    <ui:Button
                        x:Name="FontBold"
                        Background="#FFAAAAAA"
                        Click="FontBold_Click"
                        Cursor="Hand"
                        Icon="{ui:SymbolIcon textBold16,
                                             Filled=False}"
                        ToolTip="Font Bold" />
                    <ui:Button
                        x:Name="FontShadow"
                        Background="#FFAAAAAA"
                        Click="FontShadow_Click"
                        Icon="{ui:SymbolIcon squareShadow12,
                                             Filled=False}"
                        ToolTip="Font Shadow" />
                    <ui:Button
                        x:Name="OpacityIncrease"
                        Margin="5,0,0,0"
                        Background="#FFAAAAAA"
                        Click="OpacityIncrease_Click"
                        Cursor="Hand"
                        Icon="{ui:SymbolIcon arrowAutofitUp20,
                                             Filled=False}"
                        ToolTip="Opacity Increase" />
                    <ui:Button
                        x:Name="OpacityDecrease"
                        Background="#FFAAAAAA"
                        Click="OpacityDecrease_Click"
                        Cursor="Hand"
                        Icon="{ui:SymbolIcon arrowAutofitDown20,
                                             Filled=False}"
                        ToolTip="Opacity Decrease" />
                    <ui:Button
                        x:Name="BackgroundColorCycle"
                        Background="#FFAAAAAA"
                        Click="BackgroundColorCycle_Click"
                        Cursor="Hand"
                        Icon="{ui:SymbolIcon color16,
                                             Filled=False}"
                        ToolTip="Background Color" />

                    <ui:Button
                        x:Name="OnlyModeButton"
                        Margin="5,0,0,0"
                        Background="#FFAAAAAA"
                        Click="OnlyModeButton_Click"
                        Cursor="Hand"
                        Icon="{ui:SymbolIcon PanelBottom20,
                                             Filled=False}"
                        ToolTip="Show only subtitles or translations" />
                    <ui:Button
                        x:Name="ClickThrough"
                        Background="#FFAAAAAA"
                        Click="ClickThrough_Click"
                        Cursor="Hand"
                        Icon="{ui:SymbolIcon lockClosed16,
                                             Filled=False}"
                        ToolTip="Click Through" />
                </StackPanel>
            </Grid>
        </Border>

        <Thumb
            x:Name="TopThumb"
            Height="10"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Top"
            Cursor="SizeNS"
            DragDelta="TopThumb_OnDragDelta"
            Style="{StaticResource TransparentThumbStyle}" />
        <Thumb
            x:Name="BottomThumb"
            Height="10"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Bottom"
            Cursor="SizeNS"
            DragDelta="BottomThumb_OnDragDelta"
            Style="{StaticResource TransparentThumbStyle}" />
        <Thumb
            x:Name="LeftThumb"
            Width="10"
            HorizontalAlignment="Left"
            VerticalAlignment="Stretch"
            Cursor="SizeWE"
            DragDelta="LeftThumb_OnDragDelta"
            Style="{StaticResource TransparentThumbStyle}" />
        <Thumb
            x:Name="RightThumb"
            Width="10"
            HorizontalAlignment="Right"
            VerticalAlignment="Stretch"
            Cursor="SizeWE"
            DragDelta="RightThumb_OnDragDelta"
            Style="{StaticResource TransparentThumbStyle}" />
        <Thumb
            x:Name="TopLeftThumb"
            Width="10"
            Height="10"
            HorizontalAlignment="Left"
            VerticalAlignment="Top"
            Cursor="SizeNWSE"
            DragDelta="TopLeftThumb_OnDragDelta"
            Style="{StaticResource TransparentThumbStyle}" />
        <Thumb
            x:Name="TopRightThumb"
            Width="10"
            Height="10"
            HorizontalAlignment="Right"
            VerticalAlignment="Top"
            Cursor="SizeNESW"
            DragDelta="TopRightThumb_OnDragDelta"
            Style="{StaticResource TransparentThumbStyle}" />
        <Thumb
            x:Name="BottomLeftThumb"
            Width="10"
            Height="10"
            HorizontalAlignment="Left"
            VerticalAlignment="Bottom"
            Cursor="SizeNESW"
            DragDelta="BottomLeftThumb_OnDragDelta"
            Style="{StaticResource TransparentThumbStyle}" />
        <Thumb
            x:Name="BottomRightThumb"
            Width="10"
            Height="10"
            HorizontalAlignment="Right"
            VerticalAlignment="Bottom"
            Cursor="SizeNWSE"
            DragDelta="BottomRightThumb_OnDragDelta"
            Style="{StaticResource TransparentThumbStyle}" />
    </Grid>
</Window>
