﻿<ui:FluentWindow
    x:Class="LiveCaptionsTranslator.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:LiveCaptionsTranslator"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="LiveCaptions Translator"
    Width="750"
    Height="170"
    MinWidth="750"
    MinHeight="170"
    Background="Transparent"
    ExtendsContentIntoTitleBar="True"
    LocationChanged="MainWindow_LocationChanged"
    ResizeMode="CanResizeWithGrip"
    SizeChanged="MainWindow_SizeChanged"
    Topmost="True"
    WindowBackdropType="Mica"
    WindowStartupLocation="Manual"
    WindowStyle="None"
    mc:Ignorable="d">

    <Grid>
        <Grid x:Name="MainContent">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <ui:TitleBar
                Grid.Row="0"
                Height="27"
                ShowMaximize="False">
                <ui:TitleBar.Header>
                    <ui:TextBlock Padding="15,0,0,0" VerticalAlignment="Center">LiveCaptions Translator</ui:TextBlock>
                </ui:TitleBar.Header>
                <ui:TitleBar.TrailingContent>
                    <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                        <ui:Button
                            x:Name="CaptionLogButton"
                            Margin="0,0,5,0"
                            Appearance="Transparent"
                            Click="CaptionLogButton_Click"
                            Icon="{ui:SymbolIcon History16,
                                                 Filled=False}"
                            ToolTip="Log Cards of Captions"
                            Visibility="Collapsed" />
                        <ui:Button
                            x:Name="LogOnlyButton"
                            Margin="0,0,5,0"
                            Appearance="Transparent"
                            Click="LogOnlyButton_Click"
                            Icon="{ui:SymbolIcon Pause16,
                                                 Filled=False}"
                            ToolTip="Pause Translation (Log Only)" />
                        <ui:Button
                            x:Name="OverlayModeButton"
                            Margin="0,0,5,0"
                            Appearance="Transparent"
                            Click="OverlayModeButton_Click"
                            Icon="{ui:SymbolIcon ClosedCaptionOff24,
                                                 Filled=False}"
                            ToolTip="Overlay Window" />
                        <ui:Button
                            x:Name="TopmostButton"
                            Margin="0,0,5,0"
                            Appearance="Transparent"
                            Click="TopmostButton_Click"
                            Icon="{ui:SymbolIcon Pin16,
                                                 Filled=True}"
                            ToolTip="Always on Top" />
                    </StackPanel>
                </ui:TitleBar.TrailingContent>
            </ui:TitleBar>

            <ui:NavigationView
                x:Name="RootNavigation"
                Grid.Row="1"
                Height="Auto"
                HeaderVisibility="Collapsed"
                IsBackButtonVisible="Collapsed"
                IsPaneOpen="False"
                IsPaneToggleVisible="False"
                PaneDisplayMode="LeftMinimal">
                <ui:NavigationView.MenuItems>
                    <ui:NavigationViewItem
                        Margin="0,6,0,3"
                        Content="Caption"
                        FontFamily="Bahnschrift"
                        Icon="{ui:SymbolIcon Home24}"
                        NavigationCacheMode="Enabled"
                        TargetPageType="{x:Type local:CaptionPage}" />
                    <ui:NavigationViewItem
                        Margin="0,3,0,0"
                        Content="Setting"
                        FontFamily="Bahnschrift"
                        Icon="{ui:SymbolIcon Settings24}"
                        NavigationCacheMode="Required"
                        TargetPageType="{x:Type local:SettingPage}" />
                    <ui:NavigationViewItem
                        Margin="0,3,0,0"
                        Content="History"
                        FontFamily="Bahnschrift"
                        Icon="{ui:SymbolIcon History24}"
                        NavigationCacheMode="Disabled"
                        TargetPageType="{x:Type local:HistoryPage}" />
                    <ui:NavigationViewItem
                        Margin="0,3,0,0"
                        Content="Info"
                        FontFamily="Bahnschrift"
                        Icon="{ui:SymbolIcon Info24}"
                        NavigationCacheMode="Required"
                        TargetPageType="{x:Type local:InfoPage}" />
                </ui:NavigationView.MenuItems>
            </ui:NavigationView>
        </Grid>
        <ui:SnackbarPresenter x:Name="SnackbarHost" Grid.Row="0" />
        <ContentPresenter
            x:Name="DialogHostContainer"
            MinHeight="167"
            Visibility="Collapsed" />
    </Grid>
</ui:FluentWindow>
