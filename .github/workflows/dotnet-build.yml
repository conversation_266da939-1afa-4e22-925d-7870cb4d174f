name: CI/CD Pipeline

on:
  push:
    branches:
      - '*'
    tags:
      - 'v*'
    paths:
      - 'src/**'
      - 'Properties/**'
  pull_request:
  workflow_dispatch:

jobs:
  build:
    if: >
      github.event_name == 'workflow_dispatch' ||
      github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v') ||
      contains(github.event.head_commit.message, 'feat') ||
      contains(github.event.head_commit.message, 'fix') ||
      contains(github.event.head_commit.message, 'perf') ||
      contains(github.event.head_commit.message, 'build')
    name: Build and Test
    runs-on: windows-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.x'

      - name: Restore Dependencies
        run: dotnet restore

      - name: Build
        run: |
          dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:EnableCompressionInSingleFile=true -o ./publish/x64/selfcontained
          del ./publish/x64/selfcontained/*.pdb 2>nul || echo "No pdb found in x64 selfcontained"

          dotnet publish -c Release -r win-x64 --self-contained false -p:PublishSingleFile=true -o ./publish/x64/framework
          del ./publish/x64/framework/*.pdb 2>nul || echo "No pdb found in x64 framework"

          dotnet publish -c Release -r win-arm64 --self-contained true -p:PublishSingleFile=true -p:EnableCompressionInSingleFile=true -o ./publish/arm64/selfcontained
          del ./publish/arm64/selfcontained/*.pdb 2>nul || echo "No pdb found in arm64 selfcontained"

          dotnet publish -c Release -r win-arm64 --self-contained false -p:PublishSingleFile=true -o ./publish/arm64/framework
          del ./publish/arm64/framework/*.pdb 2>nul || echo "No pdb found in arm64 framework"

      - name: Run Tests
        run: dotnet test --verbosity normal

      - name: Upload Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: LiveCaptionsTranslator
          path: ./publish

  release:
    name: Create GitHub Release
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')

    steps:
      - name: Download Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: LiveCaptionsTranslator
          path: ./release

      - name: Create ZIP Files and rename EXE
        run: |
          cd release
          mv x64/framework/LiveCaptionsTranslator.exe LiveCaptionsTranslator-win-x64.exe
          mv arm64/framework/LiveCaptionsTranslator.exe LiveCaptionsTranslator-win-arm64.exe
          mv x64/selfcontained/LiveCaptionsTranslator.exe LiveCaptionsTranslator-win-x64-withruntime.exe
          mv arm64/selfcontained/LiveCaptionsTranslator.exe LiveCaptionsTranslator-win-arm64-withruntime.exe

      - name: Publish GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          files: |
            release/LiveCaptionsTranslator-win-x64.exe
            release/LiveCaptionsTranslator-win-x64-withruntime.exe
            release/LiveCaptionsTranslator-win-arm64.exe
            release/LiveCaptionsTranslator-win-arm64-withruntime.exe
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}