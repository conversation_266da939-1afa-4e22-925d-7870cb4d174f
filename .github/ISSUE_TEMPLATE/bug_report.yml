﻿---
name: Bug Report
description: Create a bug report to help us improve
labels: bug

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
        
        _The more information you share, the faster we can identify and fix the bug._
        Prior to opening the issue, please make sure that you:

        - Use English in the title and description is required. You can also add your preferred language.
        - Search the [issues](https://github.com/SakiRinn/LiveCaptions-Translator/issues)/[wiki](https://github.com/SakiRinn/LiveCaptions-Translator/wiki)/[discussions](https://github.com/SakiRinn/LiveCaptions-Translator/discussions) to avoid duplicating the issue.
  - type: textarea
    id: bug-description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is. If you intend to submit a PR for this issue, tell us in the description. Thanks!
      placeholder: |
        What is the issue with the current behavior?
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: Reproduction
      description: Steps to reproduce the behavior.
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Using '...' API
        4. Dealing '...' voice
        5. See error
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: Expected behavior
    validations:
      required: true
  - type: textarea
    id: config-file
    attributes:
      label: Config file
      description: What is the detail of your setting.json? Remember to delete your api key.
      placeholder: |
        {...}
    validations:
      required: true
  - type: textarea
    id: system-info
    attributes:
      label: System Info
      description: Share your environment details. Reports without proper, valid environment details will likely be closed.
      render: Text
      placeholder: |
        - OS: [e.g. Windows 11 24H2 26100.3194]
        - API using [e.g. G Translate, OpenAI]
        - Version [e.g. v1.2]
    validations:
      required: true
  - type: textarea
    id: additional-context
    attributes:
      label: Additional context
      description: Add any other context or screenshots about the bug report here.
  - type: checkboxes
    id: checkboxes
    attributes:
      label: Validations
      description: Before submitting the issue, please make sure you do the following
      options:
        - label: I confirm that I am submitting a bug
          required: true
        - label: I have read all the [issues](https://github.com/SakiRinn/LiveCaptions-Translator/issues)/[wiki](https://github.com/SakiRinn/LiveCaptions-Translator/wiki) and made sure there is no duplicate
          required: true
        - label: I have tried the latest version, updated system the issue still exists
          required: true
        - label: I have checked if the issue is related to my environment configuration, network, etc.
          required: true
        - label: Besides my preferred language, I have added English in the title and description
          required: true          
        - label: I will be polite and respectful in my communication
          required: true