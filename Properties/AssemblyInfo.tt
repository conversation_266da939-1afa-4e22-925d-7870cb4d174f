﻿<#@ template language="C#" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Linq" #>

//
// This code was generated by a tool. Any changes made manually will be lost
// the next time this code is regenerated.
//

using System.Reflection;

<#
    var date = DateTime.Now;
    int major = 1;
    int minor = 6;
    int build = int.Parse(date.ToString("yyMM")) / 2;
    int revision = int.Parse(date.ToString("ddHH"));
#>

[assembly: AssemblyTitle("LiveCaptions Translator")]
[assembly: AssemblyVersion("<#= $"{major}.{minor}.{build}.{revision}" #>")]
[assembly: AssemblyFileVersion("<#= $"{major}.{minor}.{build}.{revision}" #>")]