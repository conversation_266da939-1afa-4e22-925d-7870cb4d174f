﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net8.0-windows</TargetFramework>
		<SupportedOSPlatformVersion>7.0</SupportedOSPlatformVersion>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UseWPF>true</UseWPF>
		<RuntimeIdentifiers>win-x64;win-arm64</RuntimeIdentifiers>
		<PublishReadyToRun>false</PublishReadyToRun>
		<PublishSingleFile>true</PublishSingleFile>
		<SelfContained>false</SelfContained>
		<IncludeAllContentForSelfExtract>true</IncludeAllContentForSelfExtract>

		<Title>LiveCaptions Translator</Title>
		<Description>A real-time speech translation tool based on Windows LiveCaptions.</Description>
		<Copyright>Copyright (c) 2024 SakiRinn and other contributors</Copyright>
		<Authors>SakiRinn</Authors>
		<ApplicationIcon>src\LiveCaptions-Translator.ico</ApplicationIcon>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<Platforms>AnyCPU;ARM64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<ApplicationDefinition Include="src\App.xaml">
			<Generator>MSBuild:Compile</Generator>
			<SubType>Designer</SubType>
		</ApplicationDefinition>
		<Content Include="src\LiveCaptions-Translator.ico" />
		
		<PackageReference Include="Interop.UIAutomationClient" Version="10.19041.0" />
		
		<PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.1" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.Json" Version="8.0.5" />
		<PackageReference Include="WPF-UI" Version="4.0.1" />
	</ItemGroup>

	<ItemGroup>
	  <None Update="Properties\AssemblyInfo.tt">
	    <Generator>TextTemplatingFileGenerator</Generator>
	    <LastGenOutput>AssemblyInfo.cs</LastGenOutput>
	  </None>
	</ItemGroup>

	<ItemGroup>
	  <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Properties\AssemblyInfo.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>AssemblyInfo.tt</DependentUpon>
	  </Compile>
	</ItemGroup>

</Project>
